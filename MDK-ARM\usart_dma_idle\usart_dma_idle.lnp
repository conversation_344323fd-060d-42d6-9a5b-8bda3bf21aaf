--cpu Cortex-M3
"usart_dma_idle\startup_stm32f103xb.o"
"usart_dma_idle\main.o"
"usart_dma_idle\gpio.o"
"usart_dma_idle\dma.o"
"usart_dma_idle\usart.o"
"usart_dma_idle\stm32f1xx_it.o"
"usart_dma_idle\stm32f1xx_hal_msp.o"
"usart_dma_idle\stm32f1xx_hal_gpio_ex.o"
"usart_dma_idle\stm32f1xx_hal_uart.o"
"usart_dma_idle\stm32f1xx_hal.o"
"usart_dma_idle\stm32f1xx_hal_rcc.o"
"usart_dma_idle\stm32f1xx_hal_rcc_ex.o"
"usart_dma_idle\stm32f1xx_hal_gpio.o"
"usart_dma_idle\stm32f1xx_hal_dma.o"
"usart_dma_idle\stm32f1xx_hal_cortex.o"
"usart_dma_idle\stm32f1xx_hal_pwr.o"
"usart_dma_idle\stm32f1xx_hal_flash.o"
"usart_dma_idle\stm32f1xx_hal_flash_ex.o"
"usart_dma_idle\stm32f1xx_hal_exti.o"
"usart_dma_idle\system_stm32f1xx.o"
"usart_dma_idle\usart_app.o"
"usart_dma_idle\scheduler.o"
--strict --scatter "usart_dma_idle\usart_dma_idle.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "usart_dma_idle.map" -o usart_dma_idle\usart_dma_idle.axf