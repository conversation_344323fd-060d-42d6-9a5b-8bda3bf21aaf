#include "scheduler.h"


// ȫ�ֱ��������ڴ洢��������
uint8_t task_num;

typedef struct {
    void (*task_func)(void);
    uint32_t rate_ms;
    uint32_t last_run;
} task_t;

// ��̬�������飬ÿ�����������������ִ�����ڣ����룩���ϴ�����ʱ�䣨���룩
static task_t scheduler_task[] =
{
		{uart_task, 5, 0},  // ����һ������������Ϊ Uart_Proc��ִ������Ϊ 5 ���룬��ʼ�ϴ�����ʱ��Ϊ 0
};

/**
 * @brief ��������ʼ������
 * �������������Ԫ�ظ�������������洢�� task_num ��
 */
void scheduler_init(void)
{
    // �������������Ԫ�ظ�������������洢�� task_num ��
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

/**
 * @brief ���������к���
 * �����������飬����Ƿ���������Ҫִ�С������ǰʱ���Ѿ����������ִ�����ڣ���ִ�и����񲢸����ϴ�����ʱ��
 */
void scheduler_run(void)
{
    uint32_t current_time = HAL_GetTick();
    
    for (uint8_t i = 0; i < task_num; i++)
    {
        // 检查是否到了执行时间
        if ((current_time - scheduler_task[i].last_run) >= scheduler_task[i].rate_ms)
        {
            // 更新上次执行时间
            scheduler_task[i].last_run = current_time;
            
            // 执行任务
            if (scheduler_task[i].task_func != NULL)
            {
                scheduler_task[i].task_func();
            }
        }
    }
}


